/**
 * Transportation Reservations Management
 * Handles external reservation modal and localStorage operations
 */

// Global object to expose functions to other scripts
window.transportationReservations = {};

document.addEventListener('DOMContentLoaded', function () {
    // DOM Elements
    const addExternalReservationBtn = document.getElementById('addExternalReservation');
    const externalReservationModal = document.getElementById('externalReservationModal');

    // Check if modal exists before trying to access its properties
    if (!externalReservationModal) {
        console.error('External reservation modal not found in the DOM');
        return;
    }

    const closeModalBtns = externalReservationModal.querySelectorAll('.close-modal, .cancel-modal-btn');
    const externalReservationForm = document.getElementById('externalReservationForm');
    const reservedList = document.getElementById('reservedList');

    // Date pickers are now initialized in date-picker-init.js

    // Load and display existing reservations
    loadReservations();

    // Event Listeners

    // Open modal when Add External Booking button is clicked
    if (addExternalReservationBtn) {
        console.log('Add External Reservation button found, adding click event listener');
        addExternalReservationBtn.addEventListener('click', function () {
            console.log('Add External Reservation button clicked');
            openExternalReservationModal();
        });
    } else {
        console.error('Add External Reservation button not found in the DOM');
    }

    // Close modal when close button or cancel button is clicked
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            closeExternalReservationModal();
        });
    });

    // Close modal when clicking outside the modal content
    window.addEventListener('click', function (event) {
        if (event.target === externalReservationModal) {
            closeExternalReservationModal();
        }
    });

    // Trip type selection removed as reservations are logically one-way

    // Handle form submission
    if (externalReservationForm) {
        externalReservationForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            await saveReservation();
        });
    }

    // Functions

    // Open the external reservation modal
    function openExternalReservationModal(reservationToEdit = null) {
        // Make this function available globally
        window.transportationReservations.openExternalReservationModal = openExternalReservationModal;
        console.log('Opening external reservation modal');

        // Check if modal and form exist
        if (!externalReservationModal || !externalReservationForm) {
            console.error('Modal or form not found');
            return;
        }

        // Reset form
        externalReservationForm.reset();

        // Check if externalReservationId exists
        const reservationIdField = document.getElementById('externalReservationId');
        if (reservationIdField) {
            reservationIdField.value = '';
        } else {
            console.error('externalReservationId field not found');
        }

        // Trip type and return date removed as reservations are logically one-way

        // If editing an existing reservation, populate the form
        if (reservationToEdit) {
            document.getElementById('externalReservationId').value = reservationToEdit.id;
            document.getElementById('bookingType').value = reservationToEdit.type;
            document.getElementById('externalFrom').value = reservationToEdit.from;
            document.getElementById('externalTo').value = reservationToEdit.to;
            document.getElementById('externalDate').value = reservationToEdit.date;
            document.getElementById('externalTime').value = reservationToEdit.time;
            document.getElementById('externalCarrier').value = reservationToEdit.carrier;
            document.getElementById('externalReference').value = reservationToEdit.reference;
            document.getElementById('externalNotes').value = reservationToEdit.notes;

            // Return date handling removed as reservations are logically one-way
        }

        // Show modal
        console.log('Displaying modal');
        externalReservationModal.style.display = 'block';
    }

    // Close the external reservation modal
    function closeExternalReservationModal() {
        externalReservationModal.style.display = 'none';
    }

    // Save reservation to localStorage and Supabase if user is logged in
    async function saveReservation() {
        // Make this function available globally
        window.transportationReservations.saveReservation = saveReservation;
        // Get form data
        const reservationId = document.getElementById('externalReservationId').value || generateId();
        const type = document.getElementById('bookingType').value;
        const from = document.getElementById('externalFrom').value;
        const to = document.getElementById('externalTo').value;
        const date = document.getElementById('externalDate').value;
        const time = document.getElementById('externalTime').value;
        const carrier = document.getElementById('externalCarrier').value;
        const reference = document.getElementById('externalReference').value;
        const notes = document.getElementById('externalNotes').value;
        // Create reservation object
        const reservation = {
            id: reservationId,
            type: type,
            from: from,
            to: to,
            date: date,
            time: time,
            carrier: carrier,
            reference: reference,
            notes: notes,
            tripType: 'one-way', // Always one-way as reservations are logically one-way
            createdAt: new Date().toISOString()
        };

        // Get existing reservations
        const reservations = await getReservations();

        // Check if we're editing an existing reservation
        const existingIndex = reservations.findIndex(r => r.id === reservationId);

        if (existingIndex !== -1) {
            // Update existing reservation
            reservations[existingIndex] = reservation;
        } else {
            // Add new reservation
            reservations.push(reservation);
        }

        // Save to localStorage
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // If user is logged in, also save to Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Saving reservation to Supabase for user ${auth.user.id}`);

                // Add user_id to the reservation for Supabase
                const supabaseReservation = {
                    ...reservation,
                    user_id: auth.user.id
                };

                // Save to Supabase via Netlify function
                window.itineraryAPI.saveTransportationReservation(supabaseReservation)
                    .then(result => {
                        console.log('Reservation saved to Supabase:', result);
                    })
                    .catch(error => {
                        console.error('Error saving reservation to Supabase:', error);
                        // Continue with local storage only if Supabase save fails
                    });
            } catch (error) {
                console.error('Error saving reservation to Supabase:', error);
                // Continue with local storage only if Supabase save fails
            }
        } else {
            console.log('User is not logged in, saving to localStorage only');
        }

        // Close modal
        closeExternalReservationModal();

        // Refresh reservations list
        loadReservations();
    }

    // Load reservations from localStorage and Supabase, then display them
    async function loadReservations() {
        // Show loading state
        reservedList.innerHTML = `
            <div class="loading-reservations">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading reservations...</p>
            </div>
        `;

        try {
            // Get reservations (this now includes Supabase sync)
            const reservations = await getReservations();

            // Clear the list
            reservedList.innerHTML = '';

            if (reservations.length === 0) {
                // Show empty state
                reservedList.innerHTML = `
                    <div class="empty-reservations">
                        <i class="fas fa-ticket-alt"></i>
                        <p>No reservations yet</p>
                        <p class="hint">Your booked transportation will appear here</p>
                    </div>
                `;
                return;
            }

            // Sort reservations by date (earliest first)
            reservations.sort((a, b) => {
                const dateA = new Date(a.date + 'T' + (a.time || '00:00'));
                const dateB = new Date(b.date + 'T' + (b.time || '00:00'));
                return dateA - dateB;
            });

            // Create reservation cards
            reservations.forEach(reservation => {
                const card = createReservationCard(reservation);
                reservedList.appendChild(card);
            });
        } catch (error) {
            console.error('Error loading reservations:', error);

            // Show error state
            reservedList.innerHTML = `
                <div class="error-reservations">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading reservations</p>
                    <p class="hint">Please try again later</p>
                </div>
            `;
        }
    }

    // Create a reservation card element
    function createReservationCard(reservation) {
        const card = document.createElement('div');
        card.className = 'reservation-card';
        card.dataset.id = reservation.id;

        // Format date
        const formattedDate = formatDate(reservation.date);

        // Get icon based on type
        let typeIcon = 'fa-plane';
        if (reservation.type === 'train') {
            typeIcon = 'fa-train';
        } else if (reservation.type === 'bus') {
            typeIcon = 'fa-bus';
        }

        // Extract IATA codes from the from/to fields
        const fromCode = extractIATACode(reservation.from);
        const toCode = extractIATACode(reservation.to);

        card.innerHTML = `
            <div class="reservation-header">
                <div class="reservation-type">
                    <i class="fas ${typeIcon}"></i>
                    <span>${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)}</span>
                </div>
                <div class="reservation-actions">
                    <button class="edit-reservation" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-reservation" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="reservation-route">
                <div class="route-point">
                    <span class="point-label">From:</span>
                    <span class="point-value">${fromCode}</span>
                </div>
                <div class="route-arrow">
                    <i class="fas fa-long-arrow-alt-right"></i>
                </div>
                <div class="route-point">
                    <span class="point-label">To:</span>
                    <span class="point-value">${toCode}</span>
                </div>
            </div>
            <div class="reservation-details">
                <div class="detail-item">
                    <i class="far fa-calendar-alt"></i>
                    <span>${formattedDate}</span>
                </div>
                ${reservation.time ? `
                <div class="detail-item">
                    <i class="far fa-clock"></i>
                    <span>${formatTime(reservation.time)}</span>
                </div>
                ` : ''}
                ${reservation.carrier ? `
                <div class="detail-item">
                    <i class="fas fa-building"></i>
                    <span>${reservation.carrier}</span>
                </div>
                ` : ''}
                ${reservation.reference ? `
                <div class="detail-item">
                    <i class="fas fa-hashtag"></i>
                    <span>Ref: ${reservation.reference}</span>
                </div>
                ` : ''}
            </div>
            ${reservation.notes ? `
            <div class="reservation-notes">
                <i class="fas fa-sticky-note"></i>
                <p>${reservation.notes}</p>
            </div>
            ` : ''}
            <div class="reservation-footer">
                <button class="add-to-itinerary-btn">
                    <i class="fas fa-calendar-plus"></i> Add to Itinerary
                </button>
            </div>
        `;

        // Add event listeners to the card buttons

        // Edit button
        card.querySelector('.edit-reservation').addEventListener('click', async function () {
            const reservationId = card.dataset.id;
            const reservations = await getReservations();
            const reservation = reservations.find(r => r.id === reservationId);
            if (reservation) {
                openExternalReservationModal(reservation);
            }
        });

        // Delete button
        card.querySelector('.delete-reservation').addEventListener('click', function () {
            const reservationId = card.dataset.id;
            if (confirm('Are you sure you want to delete this reservation?')) {
                deleteReservation(reservationId);
            }
        });

        // Add to Itinerary button
        card.querySelector('.add-to-itinerary-btn').addEventListener('click', function () {
            const reservationId = card.dataset.id;
            addToItinerary(reservationId);
        });

        return card;
    }

    // Delete a reservation from localStorage and Supabase if user is logged in
    function deleteReservation(id) {
        let reservations = getReservations();
        reservations = reservations.filter(r => r.id !== id);
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // If user is logged in, also delete from Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Deleting reservation ${id} from Supabase for user ${auth.user.id}`);
                window.itineraryAPI.deleteTransportationReservation(auth.user.id, id)
                    .then(result => {
                        console.log('Reservation deleted from Supabase:', result);
                    })
                    .catch(error => {
                        console.error('Error deleting reservation from Supabase:', error);
                        // Continue with local storage update even if Supabase delete fails
                    });
            } catch (error) {
                console.error('Error deleting reservation from Supabase:', error);
                // Continue with local storage update even if Supabase delete fails
            }
        } else {
            console.log('User is not logged in, deleting from localStorage only');
        }

        loadReservations();
    }

    // Add reservation to itinerary
    async function addToItinerary(id) {
        // Make this function available globally
        window.transportationReservations.addToItinerary = addToItinerary;
        const reservations = await getReservations();
        const reservation = reservations.find(r => r.id === id);

        if (!reservation) return;

        // Create event data for the itinerary
        const eventData = {
            title: `${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)} to ${reservation.to}`,
            location: `From: ${reservation.from} To: ${reservation.to}`,
            startDate: reservation.date,
            startTime: reservation.time || '00:00',
            info: `Carrier: ${reservation.carrier || 'N/A'}\nReference: ${reservation.reference || 'N/A'}\n${reservation.notes || ''}`
        };

        // Store in localStorage for the itinerary page to use
        localStorage.setItem('pendingTransportationEvent', JSON.stringify(eventData));

        // Redirect to itinerary page with parameter to open the modal
        window.location.href = 'itinerary.html?openTransportationModal=true';
    }

    // Helper Functions

    // Get reservations from localStorage and sync with Supabase if user is logged in
    async function getReservations() {
        // Make this function available globally
        window.transportationReservations.getReservations = getReservations;

        // Get reservations from localStorage
        let reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');

        // If user is logged in and itineraryAPI is available, sync with Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user && window.itineraryAPI) {
            try {
                console.log(`Syncing reservations with Supabase for user ${auth.user.id}`);

                // Sync reservations with Supabase
                const syncResult = await window.itineraryAPI.syncTransportationReservations(auth.user.id, reservations);
                console.log('Reservations synced with Supabase:', syncResult);

                // Check if we got any new or updated reservations from Supabase
                if (syncResult && syncResult.results) {
                    let hasChanges = false;

                    syncResult.results.forEach(result => {
                        if (result.success && result.reservation && result.action) {
                            // Convert Supabase reservation format to our local format
                            const supabaseReservation = {
                                id: result.reservation.reservation_id,
                                type: result.reservation.type,
                                from: result.reservation.from_location,
                                to: result.reservation.to_location,
                                date: result.reservation.departure_date,
                                time: result.reservation.departure_time,
                                carrier: result.reservation.carrier,
                                reference: result.reservation.reference,
                                notes: result.reservation.notes,
                                tripType: result.reservation.trip_type || 'one-way',
                                createdAt: result.reservation.created_at
                            };

                            // If this is a new or updated reservation, add/update it in our local reservations
                            if (result.action === 'inserted' || result.action === 'updated') {
                                // Check if we already have this reservation locally
                                const existingIndex = reservations.findIndex(r => r.id === supabaseReservation.id);

                                if (existingIndex !== -1) {
                                    // Update existing reservation
                                    reservations[existingIndex] = supabaseReservation;
                                } else {
                                    // Add new reservation
                                    reservations.push(supabaseReservation);
                                }

                                hasChanges = true;
                            }
                        }
                    });

                    // If we made changes, save back to localStorage
                    if (hasChanges) {
                        localStorage.setItem('transportationReservations', JSON.stringify(reservations));
                        console.log('Updated reservations in localStorage with data from Supabase');
                    }
                }
            } catch (syncError) {
                console.error('Error syncing reservations with Supabase:', syncError);
                // Continue with local storage data if Supabase sync fails
            }
        } else {
            console.log('User is not logged in or itineraryAPI is not available, using local storage only');
        }

        return reservations;
    }

    // Generate a unique ID
    function generateId() {
        return 'res_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Format date for display
    function formatDate(dateString) {
        const options = { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' };
        return new Date(dateString).toLocaleDateString('en-US', options);
    }

    // Extract IATA code from airport string
    function extractIATACode(airportString) {
        if (!airportString) return '';

        // Try to extract a 3-letter code in parentheses like "New York (JFK)"
        const codeMatch = airportString.match(/\(([A-Z]{3})\)/);
        if (codeMatch && codeMatch[1]) {
            return codeMatch[1];
        }

        // If no parentheses, check if the string starts with a 3-letter code
        const startCodeMatch = airportString.match(/^([A-Z]{3})/);
        if (startCodeMatch && startCodeMatch[1]) {
            return startCodeMatch[1];
        }

        // If no code found, return the first 3 characters or the whole string if shorter
        return airportString.length > 3 ? airportString.substring(0, 3).toUpperCase() : airportString.toUpperCase();
    }

    // Format time for display
    function formatTime(timeString) {
        if (!timeString) return '';

        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const hour12 = hour % 12 || 12;

        return `${hour12}:${minutes} ${ampm}`;
    }

    // Add a reservation directly (used by flight search results)
    function addReservation(reservationData) {
        console.log('Adding reservation:', reservationData);

        // Generate a unique ID
        const reservationId = generateId();

        // Create the full reservation object
        const reservation = {
            id: reservationId,
            type: reservationData.type || 'flight',
            from: reservationData.from,
            to: reservationData.to,
            date: reservationData.date,
            time: reservationData.departureTime || '',
            carrier: reservationData.carrier || '',
            reference: reservationData.flightNumber || reservationData.reference || '',
            notes: reservationData.notes || '',
            tripType: 'one-way',
            createdAt: new Date().toISOString()
        };

        // Get existing reservations (this is synchronous here since we need to return the ID)
        const reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');

        // Add new reservation
        reservations.push(reservation);

        // Save to localStorage
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // If user is logged in, also save to Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Saving new reservation to Supabase for user ${auth.user.id}`);

                // Add user_id to the reservation for Supabase
                const supabaseReservation = {
                    ...reservation,
                    user_id: auth.user.id
                };

                // Save to Supabase via Netlify function
                window.itineraryAPI.saveTransportationReservation(supabaseReservation)
                    .then(result => {
                        console.log('New reservation saved to Supabase:', result);
                    })
                    .catch(error => {
                        console.error('Error saving new reservation to Supabase:', error);
                        // Continue with local storage only if Supabase save fails
                    });
            } catch (error) {
                console.error('Error saving new reservation to Supabase:', error);
                // Continue with local storage only if Supabase save fails
            }
        } else {
            console.log('User is not logged in, saving new reservation to localStorage only');
        }

        // Refresh reservations list
        loadReservations();

        return reservationId;
    }

    // Make addReservation available globally
    window.transportationReservations.addReservation = addReservation;
});
